/**
 * 工具栏组件 - 参考原项目ir-engine-dev的简洁设计
 */
import React, { Fragment } from 'react';
import { Button, Space, Dropdown, Tooltip, Divider } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  DownOutlined,
  FileOutlined,
  UserOutlined,
  UploadOutlined,
  ExportOutlined,
  ImportOutlined,
  FolderOpenOutlined,
  SelectOutlined,
  ArrowsAltOutlined,
  RotateLeftOutlined,
  ExpandOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BorderOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  AimOutlined,
  GlobalOutlined,
  HomeOutlined,
  CompressOutlined,
  DragOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  undo,
  redo,
  setTransformMode,
  setTransformSpace,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace
} from '../../store/editor/editorSlice';
import { openDialog, DialogType, toggleFullscreen } from '../../store/ui/uiSlice';
import { useParams } from 'react-router-dom';
import ThreeRenderService from '../../services/ThreeRenderService';
import SceneExportService from '../../services/SceneExportService';
import { message } from 'antd';
import './Toolbar.less';

const Toolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();

  // 获取编辑器状态
  const transformMode = useAppSelector(state => state.editor.transformMode);
  const transformSpace = useAppSelector(state => state.editor.transformSpace);
  const showGrid = useAppSelector(state => state.editor.showGrid);
  const showAxes = useAppSelector(state => state.editor.showAxes);
  const isPlaying = useAppSelector(state => state.editor.isPlaying);
  const fullscreen = useAppSelector(state => state.ui.fullscreen);

  // 获取当前项目和场景信息
  const projectName = projectId || 'Demo Project';
  const sceneName = sceneId || 'Main Scene';
  const isModified = false; // 这里应该从状态中获取

  // 主菜单项
  const mainMenuItems = [
    {
      key: 'new',
      label: t('editor.newProject') || '新建项目',
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      label: t('editor.openProject') || '打开项目',
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      label: t('editor.saveScene') || '保存场景',
      icon: <SaveOutlined />,
      onClick: () => handleSaveScene()
    },
    {
      key: 'saveAs',
      label: t('editor.saveProjectAs') || '另存为',
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'export',
      label: t('editor.exportScene') || '导出场景',
      icon: <ExportOutlined />,
      onClick: () => handleExportScene()
    },
    {
      key: 'import',
      label: t('editor.importScene') || '导入场景',
      icon: <ImportOutlined />,
      onClick: () => handleImportScene()
    },
    { type: 'divider' as const },
    {
      key: 'settings',
      label: t('editor.projectSettings') || '项目设置',
      onClick: () => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))
    }
  ];

  // 处理返回仪表板
  const handleBackToDashboard = () => {
    // 这里应该实现返回仪表板的逻辑
    console.log('Back to dashboard');
  };

  // 处理保存场景
  const handleSaveScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene || !projectId || !sceneId) {
        message.error('无法保存场景：缺少必要信息');
        return;
      }

      // 这里应该从场景中提取数据并保存
      // 暂时显示成功消息
      message.success('场景保存成功');
      console.log('Save scene:', projectId, sceneId);
    } catch (error) {
      console.error('保存场景失败:', error);
      message.error('保存场景失败');
    }
  };

  // 处理导出场景
  const handleExportScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene) {
        message.error('无法导出场景：场景未加载');
        return;
      }

      const exportService = SceneExportService.getInstance();
      const blob = await exportService.exportSceneAsGLTF(scene, {
        format: 'gltf',
        includeTextures: true,
        includeMaterials: true,
        includeAnimations: true
      });

      if (blob) {
        const filename = `${sceneName || 'scene'}.gltf`;
        exportService.downloadFile(blob, filename);
        message.success('场景导出成功');
      } else {
        message.error('场景导出失败');
      }
    } catch (error) {
      console.error('导出场景失败:', error);
      message.error('导出场景失败');
    }
  };

  // 处理导入场景
  const handleImportScene = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.gltf,.glb,.json';
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        // 这里应该实现场景导入逻辑
        message.success(`场景文件 "${file.name}" 导入成功`);
        console.log('Import scene:', file.name);
      } catch (error) {
        console.error('导入场景失败:', error);
        message.error('导入场景失败');
      }
    };
    input.click();
  };

  // 处理发布
  const handlePublish = () => {
    // 这里应该实现发布逻辑
    console.log('Publish scene');
  };

  // 处理变换模式切换
  const handleTransformModeChange = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformMode(mode);
  };

  // 处理变换空间切换
  const handleTransformSpaceChange = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformSpace(space);
  };

  // 处理网格显示切换
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 处理坐标轴显示切换
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 处理播放/暂停切换
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 处理全屏切换
  const handleToggleFullscreen = () => {
    dispatch(toggleFullscreen());
  };

  // 处理撤销
  const handleUndo = () => {
    dispatch(undo());
  };

  // 处理重做
  const handleRedo = () => {
    dispatch(redo());
  };

  // 处理视图重置
  const handleResetView = () => {
    const renderService = ThreeRenderService.getInstance();
    renderService.resetCamera();
  };

  // 处理聚焦选中对象
  const handleFocusSelected = () => {
    const renderService = ThreeRenderService.getInstance();
    renderService.focusOnSelected();
  };

  return (
    <div className="editor-toolbar">
      {/* 左侧：Logo和主菜单 */}
      <div className="toolbar-section logo-section">
        <div className="logo" onClick={handleBackToDashboard}>
          <span>IR</span>
        </div>

        <Dropdown
          menu={{ items: mainMenuItems }}
          placement="bottomLeft"
          overlayClassName="toolbar-dropdown"
        >
          <Button
            type="text"
            icon={<DownOutlined />}
            className="main-menu-btn"
          />
        </Dropdown>

        <div className="toolbar-divider" />

        {/* 编辑工具 */}
        <div className="toolbar-group">
          <Tooltip title={t('editor.undo') || '撤销'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<UndoOutlined />}
              onClick={handleUndo}
            />
          </Tooltip>

          <Tooltip title={t('editor.redo') || '重做'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<RedoOutlined />}
              onClick={handleRedo}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 变换工具 */}
        <div className="toolbar-group toolbar-button-group">
          <span className="toolbar-label">变换</span>
          <Tooltip title={t('editor.select') || '选择'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<SelectOutlined />}
              className={transformMode === TransformMode.SELECT ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.SELECT)}
            />
          </Tooltip>

          <Tooltip title={t('editor.translate') || '移动'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<DragOutlined />}
              className={transformMode === TransformMode.TRANSLATE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.TRANSLATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.rotate') || '旋转'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<RotateLeftOutlined />}
              className={transformMode === TransformMode.ROTATE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.ROTATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.scale') || '缩放'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<ExpandOutlined />}
              className={transformMode === TransformMode.SCALE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.SCALE)}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 变换空间 */}
        <div className="toolbar-group toolbar-button-group">
          <span className="toolbar-label">空间</span>
          <Tooltip title={t('editor.worldSpace') || '世界空间'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<GlobalOutlined />}
              className={transformSpace === TransformSpace.WORLD ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformSpaceChange(TransformSpace.WORLD)}
            />
          </Tooltip>

          <Tooltip title={t('editor.localSpace') || '本地空间'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<HomeOutlined />}
              className={transformSpace === TransformSpace.LOCAL ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformSpaceChange(TransformSpace.LOCAL)}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 视图控制 */}
        <div className="toolbar-group">
          <Tooltip title={t('editor.resetView') || '重置视图'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<AimOutlined />}
              onClick={handleResetView}
            />
          </Tooltip>

          <Tooltip title={t('editor.focusSelected') || '聚焦选中'} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<CompressOutlined />}
              onClick={handleFocusSelected}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 显示选项 */}
        <div className="toolbar-group">
          <Tooltip title={showGrid ? (t('editor.hideGrid') || '隐藏网格') : (t('editor.showGrid') || '显示网格')} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={<BorderOutlined />}
              className={showGrid ? 'tool-button selected' : 'tool-button'}
              onClick={handleToggleGrid}
            />
          </Tooltip>

          <Tooltip title={showAxes ? (t('editor.hideAxes') || '隐藏坐标轴') : (t('editor.showAxes') || '显示坐标轴')} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={showAxes ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              className={showAxes ? 'tool-button selected' : 'tool-button'}
              onClick={handleToggleAxes}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 播放控制 */}
        <div className="toolbar-group">
          <Tooltip title={isPlaying ? (t('editor.pause') || '暂停') : (t('editor.play') || '播放')} overlayClassName="toolbar-tooltip">
            <Button
              size="small"
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              className={isPlaying ? 'tool-button selected' : 'tool-button'}
              onClick={handleTogglePlay}
            />
          </Tooltip>
        </div>
      </div>

      {/* 中间：面包屑导航 */}
      <div className="toolbar-section breadcrumb-section">
        <FileOutlined className="file-icon" />
        {projectName.split('/').map((part, index) => (
          <Fragment key={index}>
            <span className="breadcrumb-item">
              {part}
            </span>
            <span className="breadcrumb-separator">{' / '}</span>
          </Fragment>
        ))}
        <span className="breadcrumb-item active">
          {sceneName.split('.').slice(0, -1).join('.')}
        </span>
        {isModified && <span className="breadcrumb-item modified">*</span>}
      </div>

      {/* 右侧：设置和发布按钮 */}
      <div className="toolbar-section actions-section">
        {/* 全屏切换 */}
        <Tooltip title={fullscreen ? (t('editor.exitFullscreen') || '退出全屏') : (t('editor.fullscreen') || '全屏')} overlayClassName="toolbar-tooltip">
          <Button
            size="small"
            type="text"
            icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={handleToggleFullscreen}
          />
        </Tooltip>

        {/* 设置 */}
        <Tooltip title={t('editor.settings') || '设置'} overlayClassName="toolbar-tooltip">
          <Button
            size="small"
            type="text"
            icon={<SettingOutlined />}
          />
        </Tooltip>

        {/* 用户头像/信息 */}
        <Button
          type="text"
          icon={<UserOutlined />}
        />

        {/* 发布按钮 */}
        <Button
          type="primary"
          icon={<UploadOutlined />}
          size="small"
          onClick={handlePublish}
          className="publish-btn"
        >
          {t('editor.publish') || '发布'}
        </Button>
      </div>
    </div>
  );
};

export default Toolbar;
