/**
 * 工具栏样式
 */
.editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 8px;
  background: #1e1e1e;
  border-bottom: 1px solid #333;
  user-select: none;

  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 0 4px;

      .ant-btn {
        border: none;
        box-shadow: none;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.ant-btn-primary {
          background-color: #1890ff;
          
          &:hover {
            background-color: #40a9ff;
          }
        }

        &.ant-btn-text {
          color: #ccc;
          
          &:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
          }

          &.active {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }
      }
    }

    .toolbar-divider {
      width: 1px;
      height: 20px;
      background-color: #444;
      margin: 0 4px;
    }
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 8px;

    .logo {
      cursor: pointer;
      display: flex;
      align-items: center;
      width: 24px;
      height: 24px;
      background: #1890ff;
      border-radius: 4px;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #40a9ff;
      }

      span {
        color: #fff;
        font-size: 12px;
        font-weight: bold;
      }
    }

    .main-menu-btn {
      color: #fff;
      font-size: 12px;
      border: none;
      background: transparent;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .breadcrumb-section {
    display: flex;
    align-items: center;
    color: #ccc;
    font-size: 14px;
    flex: 1;
    justify-content: center;

    .file-icon {
      margin-right: 8px;
      color: #888;
    }

    .breadcrumb-item {
      color: #888;
      
      &.active {
        color: #fff;
      }

      &.modified {
        color: #ff6b6b;
      }
    }

    .breadcrumb-separator {
      color: #888;
      margin: 0 4px;
    }
  }

  .actions-section {
    display: flex;
    align-items: center;
    gap: 8px;

    .publish-btn {
      background: #1890ff;
      border-color: #1890ff;
      border-radius: 6px;
      
      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }

  // 工具按钮状态
  .tool-button {
    &.selected {
      background-color: #1890ff !important;
      color: #fff !important;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:hover {
        background-color: transparent !important;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .breadcrumb-section {
      display: none;
    }
  }

  @media (max-width: 768px) {
    padding: 0 4px;
    
    .toolbar-section {
      gap: 4px;
      
      .toolbar-group {
        gap: 2px;
        padding: 0 2px;
      }
    }

    .actions-section {
      gap: 4px;
    }
  }
}

// 工具提示样式
.toolbar-tooltip {
  .ant-tooltip-inner {
    background-color: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
    font-size: 12px;
  }

  .ant-tooltip-arrow::before {
    background-color: #2a2a2a;
    border: 1px solid #444;
  }
}

// 下拉菜单样式
.toolbar-dropdown {
  .ant-dropdown-menu {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    .ant-dropdown-menu-item {
      color: #ccc;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      .ant-dropdown-menu-item-icon {
        color: #888;
      }
    }

    .ant-dropdown-menu-item-divider {
      background-color: #444;
    }
  }
}

// 动画效果
@keyframes toolbarSlideIn {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.editor-toolbar {
  animation: toolbarSlideIn 0.3s ease-out;
}

// 工具栏按钮组
.toolbar-button-group {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 2px;

  .ant-btn {
    margin: 0;
    border-radius: 2px;
    
    &:not(:last-child) {
      margin-right: 2px;
    }
  }
}

// 工具栏标签
.toolbar-label {
  color: #888;
  font-size: 11px;
  margin-right: 4px;
  white-space: nowrap;
}
