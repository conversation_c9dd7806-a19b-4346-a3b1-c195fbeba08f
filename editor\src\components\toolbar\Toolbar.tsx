/**
 * 工具栏组件 - 参考原项目ir-engine-dev的简洁设计
 */
import React, { Fragment } from 'react';
import { Button, Space, Dropdown, Tooltip } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  DownOutlined,
  FileOutlined,
  UserOutlined,
  UploadOutlined,
  ExportOutlined,
  ImportOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  undo,
  redo
} from '../../store/editor/editorSlice';
import { openDialog, DialogType } from '../../store/ui/uiSlice';
import { useParams } from 'react-router-dom';
import ThreeRenderService from '../../services/ThreeRenderService';
import SceneExportService from '../../services/SceneExportService';
import { message } from 'antd';

const Toolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();

  // 获取当前项目和场景信息
  const projectName = projectId || 'Demo Project';
  const sceneName = sceneId || 'Main Scene';
  const isModified = false; // 这里应该从状态中获取

  // 主菜单项
  const mainMenuItems = [
    {
      key: 'new',
      label: t('editor.newProject') || '新建项目',
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      label: t('editor.openProject') || '打开项目',
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      label: t('editor.saveScene') || '保存场景',
      icon: <SaveOutlined />,
      onClick: () => handleSaveScene()
    },
    {
      key: 'saveAs',
      label: t('editor.saveProjectAs') || '另存为',
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'export',
      label: t('editor.exportScene') || '导出场景',
      icon: <ExportOutlined />,
      onClick: () => handleExportScene()
    },
    {
      key: 'import',
      label: t('editor.importScene') || '导入场景',
      icon: <ImportOutlined />,
      onClick: () => handleImportScene()
    },
    { type: 'divider' as const },
    {
      key: 'settings',
      label: t('editor.projectSettings') || '项目设置',
      onClick: () => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))
    }
  ];

  // 处理返回仪表板
  const handleBackToDashboard = () => {
    // 这里应该实现返回仪表板的逻辑
    console.log('Back to dashboard');
  };

  // 处理保存场景
  const handleSaveScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene || !projectId || !sceneId) {
        message.error('无法保存场景：缺少必要信息');
        return;
      }

      // 这里应该从场景中提取数据并保存
      // 暂时显示成功消息
      message.success('场景保存成功');
      console.log('Save scene:', projectId, sceneId);
    } catch (error) {
      console.error('保存场景失败:', error);
      message.error('保存场景失败');
    }
  };

  // 处理导出场景
  const handleExportScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene) {
        message.error('无法导出场景：场景未加载');
        return;
      }

      const exportService = SceneExportService.getInstance();
      const blob = await exportService.exportSceneAsGLTF(scene, {
        format: 'gltf',
        includeTextures: true,
        includeMaterials: true,
        includeAnimations: true
      });

      if (blob) {
        const filename = `${sceneName || 'scene'}.gltf`;
        exportService.downloadFile(blob, filename);
        message.success('场景导出成功');
      } else {
        message.error('场景导出失败');
      }
    } catch (error) {
      console.error('导出场景失败:', error);
      message.error('导出场景失败');
    }
  };

  // 处理导入场景
  const handleImportScene = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.gltf,.glb,.json';
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        // 这里应该实现场景导入逻辑
        message.success(`场景文件 "${file.name}" 导入成功`);
        console.log('Import scene:', file.name);
      } catch (error) {
        console.error('导入场景失败:', error);
        message.error('导入场景失败');
      }
    };
    input.click();
  };

  // 处理发布
  const handlePublish = () => {
    // 这里应该实现发布逻辑
    console.log('Publish scene');
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: '40px',
      padding: '0 16px',
      background: '#1e1e1e',
      borderBottom: '1px solid #333'
    }}>
      {/* 左侧：Logo和主菜单 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div
          style={{
            cursor: 'pointer',
            marginRight: '8px',
            display: 'flex',
            alignItems: 'center',
            width: '24px',
            height: '24px',
            background: '#1890ff',
            borderRadius: '4px',
            justifyContent: 'center'
          }}
          onClick={handleBackToDashboard}
        >
          <span style={{
            color: '#fff',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            IR
          </span>
        </div>

        <Dropdown menu={{ items: mainMenuItems }} placement="bottomLeft">
          <Button
            type="text"
            icon={<DownOutlined />}
            style={{ color: '#fff', fontSize: '12px' }}
          />
        </Dropdown>
      </div>

      {/* 中间：面包屑导航 */}
      <div style={{ display: 'flex', alignItems: 'center', color: '#ccc', fontSize: '14px' }}>
        <FileOutlined style={{ marginRight: '8px' }} />
        {projectName.split('/').map((part, index) => (
          <Fragment key={index}>
            <span style={{ color: '#888' }}>
              {part}
            </span>
            <span style={{ color: '#888', margin: '0 4px' }}>{' / '}</span>
          </Fragment>
        ))}
        <span style={{ color: '#fff' }}>
          {sceneName.split('.').slice(0, -1).join('.')}
        </span>
        {isModified && <span style={{ color: '#ff6b6b', marginLeft: '4px' }}>*</span>}
      </div>

      {/* 右侧：用户信息和发布按钮 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Space size="small">
          {/* 用户头像/信息 */}
          <Button
            type="text"
            icon={<UserOutlined />}
            style={{ color: '#fff' }}
          />

          {/* 发布按钮 */}
          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="small"
            onClick={handlePublish}
            style={{
              background: '#1890ff',
              borderColor: '#1890ff',
              borderRadius: '6px'
            }}
          >
            {t('editor.publish') || '发布'}
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default Toolbar;
